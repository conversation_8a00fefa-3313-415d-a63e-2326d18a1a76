"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Replace,
  Upload,
  X,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Image as ImageIcon,
  Trash2,
} from "lucide-react";
import { UploadDropzone } from "@/lib/uploadthing";
import { toast } from "react-hot-toast";

interface ProductImageReplacementProps {
  productId: string;
  images: string[];
  onImagesUpdated: (newImages: string[]) => void;
  className?: string;
}

interface ReplacementState {
  isOpen: boolean;
  imageIndex: number;
  currentImageUrl: string;
  newImageUrl: string;
  isUploading: boolean;
  isReplacing: boolean;
}

export default function ProductImageReplacement({
  productId,
  images,
  onImagesUpdated,
  className = "",
}: ProductImageReplacementProps) {
  const [replacement, setReplacement] = useState<ReplacementState>({
    isOpen: false,
    imageIndex: -1,
    currentImageUrl: "",
    newImageUrl: "",
    isUploading: false,
    isReplacing: false,
  });

  const [error, setError] = useState<string>("");

  const openReplacement = (index: number, imageUrl: string) => {
    setReplacement({
      isOpen: true,
      imageIndex: index,
      currentImageUrl: imageUrl,
      newImageUrl: "",
      isUploading: false,
      isReplacing: false,
    });
    setError("");
  };

  const closeReplacement = () => {
    setReplacement({
      isOpen: false,
      imageIndex: -1,
      currentImageUrl: "",
      newImageUrl: "",
      isUploading: false,
      isReplacing: false,
    });
    setError("");
  };

  const handleImageUpload = (res: any[]) => {
    if (res && res.length > 0) {
      const uploadedFile = res[0];
      setReplacement(prev => ({
        ...prev,
        newImageUrl: uploadedFile.url,
        isUploading: false,
      }));
      toast.success("Image uploaded successfully!");
    }
  };

  const handleUploadError = (error: Error) => {
    setReplacement(prev => ({ ...prev, isUploading: false }));
    setError(`Upload failed: ${error.message}`);
    toast.error("Upload failed. Please try again.");
  };

  const handleReplaceImage = async () => {
    if (!replacement.newImageUrl) {
      setError("Please upload a new image first");
      return;
    }

    setReplacement(prev => ({ ...prev, isReplacing: true }));
    setError("");

    try {
      const response = await fetch(`/api/admin/products/${productId}/replace-image`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          imageIndex: replacement.imageIndex,
          newImageUrl: replacement.newImageUrl,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Update the images array locally
        const newImages = [...images];
        newImages[replacement.imageIndex] = replacement.newImageUrl;
        onImagesUpdated(newImages);
        
        toast.success("Image replaced successfully!");
        closeReplacement();
      } else {
        setError(result.error || "Failed to replace image");
        toast.error("Failed to replace image");
      }
    } catch (error) {
      console.error("Error replacing image:", error);
      setError("Failed to replace image. Please try again.");
      toast.error("Failed to replace image");
    } finally {
      setReplacement(prev => ({ ...prev, isReplacing: false }));
    }
  };

  const handleRemoveImage = async (index: number) => {
    if (images.length <= 1) {
      toast.error("Cannot remove the last image. Products must have at least one image.");
      return;
    }

    if (!confirm("Are you sure you want to remove this image?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/products/${productId}/remove-image`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          imageIndex: index,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Update the images array locally
        const newImages = images.filter((_, i) => i !== index);
        onImagesUpdated(newImages);
        
        toast.success("Image removed successfully!");
      } else {
        toast.error(result.error || "Failed to remove image");
      }
    } catch (error) {
      console.error("Error removing image:", error);
      toast.error("Failed to remove image");
    }
  };

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Product Images ({images.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Replace images with unwanted backgrounds or elements. Click "Replace" on any image to upload a new one.
            </p>
            
            {images.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <ImageIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>No images available</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {images.map((imageUrl, index) => (
                  <div key={index} className="relative group">
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border">
                      <img
                        src={imageUrl}
                        alt={`Product image ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {/* Image overlay with actions */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => openReplacement(index, imageUrl)}
                        className="bg-white/90 hover:bg-white text-black"
                      >
                        <Replace className="h-4 w-4 mr-1" />
                        Replace
                      </Button>
                      
                      {images.length > 1 && (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleRemoveImage(index)}
                          className="bg-red-600/90 hover:bg-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    {/* Image position badge */}
                    <Badge 
                      variant="secondary" 
                      className="absolute top-2 left-2 bg-white/90 text-black"
                    >
                      {index + 1}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Replacement Dialog */}
      <Dialog open={replacement.isOpen} onOpenChange={closeReplacement}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Replace Product Image</DialogTitle>
            <DialogDescription>
              Upload a new image to replace the current one. The new image will maintain the same position in the gallery.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Current Image */}
            <div>
              <h4 className="font-medium mb-2">Current Image (Position {replacement.imageIndex + 1})</h4>
              <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden border max-w-sm">
                <img
                  src={replacement.currentImageUrl}
                  alt="Current image"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* New Image Upload */}
            <div>
              <h4 className="font-medium mb-2">New Image</h4>
              {replacement.newImageUrl ? (
                <div className="space-y-2">
                  <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden border max-w-sm">
                    <img
                      src={replacement.newImageUrl}
                      alt="New image"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex items-center gap-2 text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">New image ready for replacement</span>
                  </div>
                </div>
              ) : (
                <UploadDropzone
                  endpoint="productImageUploader"
                  onClientUploadComplete={handleImageUpload}
                  onUploadError={handleUploadError}
                  onUploadBegin={() => setReplacement(prev => ({ ...prev, isUploading: true }))}
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6"
                />
              )}
            </div>

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeReplacement} disabled={replacement.isReplacing}>
              Cancel
            </Button>
            <Button 
              onClick={handleReplaceImage} 
              disabled={!replacement.newImageUrl || replacement.isReplacing}
            >
              {replacement.isReplacing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Replacing...
                </>
              ) : (
                <>
                  <Replace className="h-4 w-4 mr-2" />
                  Replace Image
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
